import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/moment/coordinate.dart';
import 'package:user_app/models/moment/create_moment_model.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_response.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/query_comment_most_dto.dart';
import 'package:user_app/models/moment/query_moment_hot_request.dart';
import 'package:user_app/models/moment/user_moment_status_dto.dart';

class MomentApi extends BaseApi {
  static const String momentPath = 'moments';

  MomentApi(super.dio);

  Future<void> createMoment(CreateMomentModel createMomentModel) async {
    final response = await safeApiCall(
      () => dio.post(momentPath, data: createMomentModel.toJson()),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  Future<MomentResponse> getMoments(MomentListRequest request) async {
    final response = await safeApiCall(
      () => dio.post('$momentPath/list', data: request.toJson()),
      (data) => MomentResponse.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取动态列表失败");
    }

    return response.data!;
  }

  Future<MomentVo> getById(int momentId) async {
    final response = await safeApiCall(
      () => dio.get('$momentPath/$momentId'),
      (data) => MomentVo.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "未找到该动态");
    }

    return response.data!;
  }

  Future<void> likeMoment(int momentId, bool isLike) async {
    final response = await safeApiCall(
      () => dio.post('$momentPath/$momentId/like', queryParameters: {
        'isLike': isLike,
      }),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  Future<UserMomentStatusDto> getUserMomentStatus(int momentId) async {
    final response = await safeApiCall(
      () => dio.get('$momentPath/$momentId/status'),
      (data) => UserMomentStatusDto.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "获取动态状态失败");
    }

    return response.data!;
  }

  Future<MomentResponse> fetchHotMoments(
      QueryHotMomentDto queryHotMomentDto) async {
    final response = await safeApiCall(
      () => dio.post('$momentPath/hot', data: queryHotMomentDto.toJson()),
      (data) => MomentResponse.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取热门动态失败");
    }

    return response.data!;
  }

  Future<MomentResponse> fetchMostCommentedMoments(
      QueryCommentMostDto request) async {
    final response = await safeApiCall(
      () => dio.post('$momentPath/most-commented', data: request.toJson()),
      (data) => MomentResponse.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取评论最多动态失败");
    }

    return response.data!;
  }

  Future<List<Coordinate>> coordinates(MomentListRequest request) async {
    final response = await safeApiCall(
      () => dio.post('$momentPath/coordinates', data: request.toJson()),
      (data) => (data as List).map((e) => Coordinate.fromMap(e)).toList(),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取坐标失败");
    }

    return response.data!;
  }

  Future<MomentResponse> getMomentsByFishingSpot(
      int fishingSpotId, int page, int size) async {
    final response = await safeApiCall(
      () => dio.get('$momentPath/spot/$fishingSpotId', queryParameters: {
        'page': page,
        'size': size,
      }),
      (data) {
        final pageData = data as Map<String, dynamic>;
        final records = (pageData['records'] as List)
            .map((e) => MomentVo.fromMap(e))
            .toList();

        return MomentResponse(
          records: records,
          current: pageData['current'] ?? page,
          size: pageData['size'] ?? size,
          total: pageData['total'] ?? 0,
          pages: pageData['pages'] ?? 0,
        );
      },
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取钓点动态失败");
    }

    return response.data!;
  }

  Future<MomentResponse> getFollowingMoments(int page, int size) async {
    final response = await safeApiCall(
      () => dio.get('$momentPath/following', queryParameters: {
        'page': page,
        'size': size,
      }),
      (data) {
        final pageData = data as Map<String, dynamic>;
        final records = (pageData['records'] as List)
            .map((e) => MomentVo.fromMap(e))
            .toList();

        return MomentResponse(
          records: records,
          current: pageData['current'] ?? page,
          size: pageData['size'] ?? size,
          total: pageData['total'] ?? 0,
          pages: pageData['pages'] ?? 0,
        );
      },
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 500, message: "获取关注动态失败");
    }

    return response.data!;
  }
}
