import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/widgets/comment_item.dart';
import 'package:user_app/models/comment/comment_vo.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class CommentSection extends StatefulWidget {
  final int momentId;

  final void Function(int commentId, String userName) onStartReply;

  const CommentSection({
    super.key,
    required this.momentId,
    required this.onStartReply,
  });

  @override
  State<CommentSection> createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  late CommentViewModel _commentViewModel;

  @override
  void initState() {
    super.initState();
    // Use read here as we don't need to rebuild when the ViewModel instance changes.
    _commentViewModel = context.read<CommentViewModel>();
    // Load comments after the first frame is built.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _commentViewModel.loadComments(widget.momentId);
      }
    });
  }

  void _handleCommentVote(int commentId, bool isUpvote) {
    final authViewModel = context.read<AuthViewModel>();
    if (!authViewModel.isUserLoggedIn()) {
      _showLoginDialog();
      return;
    }

    _commentViewModel.voteComment(commentId, isUpvote);
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('需要登录'),
        content: const Text('请先登录后再进行点赞或反赞'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.login);
            },
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  /// 递归计算总评论数（包括所有子评论）
  int _calculateTotalCommentCount(List<CommentVo> comments) {
    int total = 0;
    for (final comment in comments) {
      total += 1; // 当前评论
      total += _calculateTotalCommentCount(comment.replies); // 递归计算子评论
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    // Use Consumer to listen for changes in the comment data.
    return Consumer<CommentViewModel>(
      builder: (context, commentViewModel, child) {
        final comments = commentViewModel.getCommentsForMoment(widget.momentId);
        final isLoading = commentViewModel.isLoading;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Comment header
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Text(
                    '评论 ${_calculateTotalCommentCount(comments)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (isLoading && comments.isEmpty)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                ],
              ),
            ),

            // Empty state for when there are no comments
            if (comments.isEmpty && !isLoading)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 48),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.mode_comment_outlined,
                        size: 48,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '还没有评论',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '快来发表第一条评论吧',
                        style: TextStyle(
                          color: Colors.grey.shade400,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            // The list of comments
            else
              ListView.builder(
                shrinkWrap: true,
                // Essential for ListView inside a Column
                physics: const NeverScrollableScrollPhysics(),
                // Parent handles scrolling
                itemCount: comments.length,
                itemBuilder: (context, index) {
                  final comment = comments[index];
                  return CommentItem(
                    comment: comment,
                    // The onReply action now calls the callback passed from the parent.
                    onReply: () =>
                        widget.onStartReply(comment.id, comment.userName),
                    onVote: (isUpvote) =>
                        _handleCommentVote(comment.id, isUpvote),
                  );
                },
              ),
            // The comment input box has been moved to MomentDetailPage.
          ],
        );
      },
    );
  }
}
